// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a ar locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'ar';

  static String m0(name) => "مرحبًا، ${name}";

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
    "absents": MessageLookupByLibrary.simpleMessage("الغياب"),
    "actions": MessageLookupByLibrary.simpleMessage("الإجراءات"),
    "active": MessageLookupByLibrary.simpleMessage("نشط"),
    "activeTasks": MessageLookupByLibrary.simpleMessage("المهام النشطة"),
    "activeTickets": MessageLookupByLibrary.simpleMessage("التذاكر النشطة"),
    "addAttachment": MessageLookupByLibrary.simpleMessage("إضافة مرفق"),
    "addClient": MessageLookupByLibrary.simpleMessage("إضافة عميل"),
    "addLeaveRequest": MessageLookupByLibrary.simpleMessage("إضافة طلب إجازة"),
    "addNewTicket": MessageLookupByLibrary.simpleMessage("إضافة تذكرة جديدة"),
    "addNote": MessageLookupByLibrary.simpleMessage("إضافة ملاحظة"),
    "agreements": MessageLookupByLibrary.simpleMessage("الاتفاقيات"),
    "allArchivedTickets": MessageLookupByLibrary.simpleMessage(
      "جميع التذاكر المؤرشفة",
    ),
    "allTickets": MessageLookupByLibrary.simpleMessage("جميع التذاكر"),
    "approveClient": MessageLookupByLibrary.simpleMessage("اعتماد العميل"),
    "approved": MessageLookupByLibrary.simpleMessage("معتمدة"),
    "arabic": MessageLookupByLibrary.simpleMessage("العربية"),
    "arabicPrint": MessageLookupByLibrary.simpleMessage("طباعة عربي"),
    "archived": MessageLookupByLibrary.simpleMessage("مؤرشف"),
    "archivedTickets": MessageLookupByLibrary.simpleMessage("تذاكري المؤرشفة"),
    "areYouSureDeleteLeave": MessageLookupByLibrary.simpleMessage(
      "هل أنت متأكد من حذف طلب الإجازة هذا؟",
    ),
    "areYouSureYouWantTo": MessageLookupByLibrary.simpleMessage(
      "هل أنت متأكد أنك تريد",
    ),
    "areYouSureYouWantToLogout": MessageLookupByLibrary.simpleMessage(
      "هل أنت متأكد أنك تريد تسجيل الخروج؟",
    ),
    "ascending": MessageLookupByLibrary.simpleMessage("تصاعدي"),
    "attachment": MessageLookupByLibrary.simpleMessage("المرفق"),
    "attachmentOptional": MessageLookupByLibrary.simpleMessage(
      "مرفق (اختياري)",
    ),
    "attendanceTime": MessageLookupByLibrary.simpleMessage("وقت الحضور"),
    "authenticateToCheckIn": MessageLookupByLibrary.simpleMessage(
      "يرجى المصادقة لتسجيل الحضور",
    ),
    "authenticateToCheckOut": MessageLookupByLibrary.simpleMessage(
      "يرجى المصادقة لتسجيل الانصراف",
    ),
    "authenticationFailed": MessageLookupByLibrary.simpleMessage(
      "فشلت المصادقة",
    ),
    "biometricNotAvailable": MessageLookupByLibrary.simpleMessage(
      "المصادقة البيومترية غير متاحة على هذا الجهاز",
    ),
    "camera": MessageLookupByLibrary.simpleMessage("الكاميرا"),
    "cancel": MessageLookupByLibrary.simpleMessage("إلغاء"),
    "changeLanguage": MessageLookupByLibrary.simpleMessage("تغيير اللغة"),
    "checkIn": MessageLookupByLibrary.simpleMessage("تسجيل الحضور"),
    "checkInFailed": MessageLookupByLibrary.simpleMessage("فشل تسجيل الحضور"),
    "checkInSuccessful": MessageLookupByLibrary.simpleMessage(
      "تم تسجيل الحضور بنجاح",
    ),
    "checkOut": MessageLookupByLibrary.simpleMessage("تسجيل الانصراف"),
    "checkOutFailed": MessageLookupByLibrary.simpleMessage(
      "فشل تسجيل الانصراف",
    ),
    "checkOutSuccessful": MessageLookupByLibrary.simpleMessage(
      "تم تسجيل الانصراف بنجاح",
    ),
    "client": MessageLookupByLibrary.simpleMessage("العميل"),
    "clientAdded": MessageLookupByLibrary.simpleMessage(
      "تم إضافة العميل بنجاح",
    ),
    "clientApproval": MessageLookupByLibrary.simpleMessage("موافقة العميل"),
    "clientID": MessageLookupByLibrary.simpleMessage("رقم العميل"),
    "clientInformation": MessageLookupByLibrary.simpleMessage("معلومات العميل"),
    "clientName": MessageLookupByLibrary.simpleMessage("اسم العميل"),
    "clientNote": MessageLookupByLibrary.simpleMessage("ملاحظة العميل"),
    "clientOpinions": MessageLookupByLibrary.simpleMessage("آراء العملاء"),
    "clientSelection": MessageLookupByLibrary.simpleMessage("اختيار العميل"),
    "clientStatus": MessageLookupByLibrary.simpleMessage("حالة العميل"),
    "clients": MessageLookupByLibrary.simpleMessage("العملاء"),
    "clientsList": MessageLookupByLibrary.simpleMessage("قائمة العملاء"),
    "companyName": MessageLookupByLibrary.simpleMessage("اسم الشركة"),
    "companyNameCannotBeEmpty": MessageLookupByLibrary.simpleMessage(
      "لا يمكن أن يكون اسم الشركة فارغًا",
    ),
    "companyRegistration": MessageLookupByLibrary.simpleMessage(
      "السجل التجاري",
    ),
    "completeAttends": MessageLookupByLibrary.simpleMessage("مجموع ايام العمل"),
    "confirm": MessageLookupByLibrary.simpleMessage("تأكيد"),
    "contractCode": MessageLookupByLibrary.simpleMessage("رمز العقد"),
    "contracts": MessageLookupByLibrary.simpleMessage("العقود"),
    "contractsExpiring": MessageLookupByLibrary.simpleMessage(
      "عقود أوشكت على الإنتهاء",
    ),
    "cost": MessageLookupByLibrary.simpleMessage("التكلفة"),
    "createdAt": MessageLookupByLibrary.simpleMessage("تاريخ الإنشاء"),
    "createdBy": MessageLookupByLibrary.simpleMessage("أنشئ بواسطة"),
    "createdDate": MessageLookupByLibrary.simpleMessage("تاريخ الإنشاء"),
    "currentTime": MessageLookupByLibrary.simpleMessage("الوقت الحالي"),
    "customerMeetings": MessageLookupByLibrary.simpleMessage("الاجتماعات"),
    "customerOpinions": MessageLookupByLibrary.simpleMessage("آراء العملاء"),
    "customers": MessageLookupByLibrary.simpleMessage("العملاء"),
    "customersList": MessageLookupByLibrary.simpleMessage("قائمة العملاء"),
    "dark": MessageLookupByLibrary.simpleMessage("داكن"),
    "days": MessageLookupByLibrary.simpleMessage("أيام"),
    "deleteLeaveRequest": MessageLookupByLibrary.simpleMessage(
      "حذف طلب الإجازة",
    ),
    "descending": MessageLookupByLibrary.simpleMessage("تنازلي"),
    "description": MessageLookupByLibrary.simpleMessage("الوصف"),
    "discount": MessageLookupByLibrary.simpleMessage("الخصم"),
    "done": MessageLookupByLibrary.simpleMessage("تم"),
    "edit": MessageLookupByLibrary.simpleMessage("تعديل"),
    "editLeaveRequest": MessageLookupByLibrary.simpleMessage(
      "تعديل طلب الإجازة",
    ),
    "editProfile": MessageLookupByLibrary.simpleMessage("تعديل الملف الشخصي"),
    "editQuotation": MessageLookupByLibrary.simpleMessage("تعديل عرض سعر"),
    "email": MessageLookupByLibrary.simpleMessage("البريد الإلكتروني"),
    "empJob": MessageLookupByLibrary.simpleMessage("المسمى الوظيفي"),
    "empName": MessageLookupByLibrary.simpleMessage("اسم الموظف"),
    "endDate": MessageLookupByLibrary.simpleMessage("تاريخ النهاية"),
    "english": MessageLookupByLibrary.simpleMessage("English"),
    "englishPrint": MessageLookupByLibrary.simpleMessage("طباعة إنجليزي"),
    "enter": MessageLookupByLibrary.simpleMessage("أدخل"),
    "enterCost": MessageLookupByLibrary.simpleMessage("أدخل التكلفة"),
    "enterDiscount": MessageLookupByLibrary.simpleMessage("أدخل الخصم"),
    "enterInstallments": MessageLookupByLibrary.simpleMessage(
      "أدخل عدد الأقساط",
    ),
    "enterMonths": MessageLookupByLibrary.simpleMessage("أدخل عدد الأشهر"),
    "enterUserLimit": MessageLookupByLibrary.simpleMessage(
      "أدخل عدد المستخدمين",
    ),
    "expired": MessageLookupByLibrary.simpleMessage("منتهية الصلاحية"),
    "expiryDate": MessageLookupByLibrary.simpleMessage("تاريخ الانتهاء"),
    "extraUserCost": MessageLookupByLibrary.simpleMessage(
      "تكلفة المستخدم الإضافي",
    ),
    "failedToGeneratePdf": MessageLookupByLibrary.simpleMessage(
      "فشل في إنشاء ملف PDF",
    ),
    "failedToLoadClients": MessageLookupByLibrary.simpleMessage(
      "فشل في تحميل العملاء",
    ),
    "failedToLoadProducts": MessageLookupByLibrary.simpleMessage(
      "فشل في تحميل المنتجات",
    ),
    "failedToSavePricing": MessageLookupByLibrary.simpleMessage(
      "فشل في حفظ التسعير",
    ),
    "failedToUpdateQuotation": MessageLookupByLibrary.simpleMessage(
      "فشل في تحديث عرض السعر",
    ),
    "failedToUpdateStatus": MessageLookupByLibrary.simpleMessage(
      "فشل في تحديث الحالة",
    ),
    "finish": MessageLookupByLibrary.simpleMessage("إنهاء"),
    "finishedTasks": MessageLookupByLibrary.simpleMessage("المهام المنجزة"),
    "finishedTickets": MessageLookupByLibrary.simpleMessage("التذاكر المؤرشفة"),
    "fromDate": MessageLookupByLibrary.simpleMessage("من تاريخ"),
    "fromDateCannotBeEmpty": MessageLookupByLibrary.simpleMessage(
      "لا يمكن أن يكون تاريخ البداية فارغًا",
    ),
    "gallery": MessageLookupByLibrary.simpleMessage("المعرض"),
    "generatingPdf": MessageLookupByLibrary.simpleMessage(
      "جاري إنشاء ملف PDF...",
    ),
    "hasExpired": MessageLookupByLibrary.simpleMessage("منتهية الصلاحية"),
    "hasLimitUsers": MessageLookupByLibrary.simpleMessage("له حد مستخدمين"),
    "hasUserLimit": MessageLookupByLibrary.simpleMessage("له حد مستخدمين"),
    "helpCenter": MessageLookupByLibrary.simpleMessage("مركز المساعدة"),
    "historyTab": MessageLookupByLibrary.simpleMessage("سجل الاجراءات"),
    "home": MessageLookupByLibrary.simpleMessage("الرئيسية"),
    "homeUpdates": MessageLookupByLibrary.simpleMessage("تحديثات الرئيسية"),
    "incompleteAttends": MessageLookupByLibrary.simpleMessage(
      "ايام بسجلات غير مكتملة",
    ),
    "initialAmount": MessageLookupByLibrary.simpleMessage("المبلغ الأولي"),
    "issue": MessageLookupByLibrary.simpleMessage("مشكلة"),
    "issueLicense": MessageLookupByLibrary.simpleMessage("اصدار عقد الترخيص"),
    "issuedAt": MessageLookupByLibrary.simpleMessage("تاريخ الإصدار"),
    "issuerEmail": MessageLookupByLibrary.simpleMessage("البريد الإلكتروني"),
    "issuerName": MessageLookupByLibrary.simpleMessage("اسم المنشئ"),
    "issuerPhone": MessageLookupByLibrary.simpleMessage("الهاتف"),
    "itsGreatToSeeYou": MessageLookupByLibrary.simpleMessage("من الرائع رؤيتك"),
    "language": MessageLookupByLibrary.simpleMessage("اللغة"),
    "leads": MessageLookupByLibrary.simpleMessage("عملاء محتملين"),
    "leaveRequestAdded": MessageLookupByLibrary.simpleMessage(
      "تم إضافة طلب الإجازة بنجاح",
    ),
    "leaveRequestDeleted": MessageLookupByLibrary.simpleMessage(
      "تم حذف طلب الإجازة بنجاح",
    ),
    "leaveRequestUpdated": MessageLookupByLibrary.simpleMessage(
      "تم تحديث طلب الإجازة بنجاح",
    ),
    "leaveRequests": MessageLookupByLibrary.simpleMessage("طلبات الإجازة"),
    "leaveType": MessageLookupByLibrary.simpleMessage("نوع الإجازة"),
    "leaves": MessageLookupByLibrary.simpleMessage("الإجازات"),
    "licenses": MessageLookupByLibrary.simpleMessage("التراخيص"),
    "licensesSubscriptions": MessageLookupByLibrary.simpleMessage(
      "التراخيص والاشتراكات",
    ),
    "light": MessageLookupByLibrary.simpleMessage("فاتح"),
    "loadingClients": MessageLookupByLibrary.simpleMessage(
      "جاري تحميل العملاء...",
    ),
    "location": MessageLookupByLibrary.simpleMessage("الموقع"),
    "login": MessageLookupByLibrary.simpleMessage("تسجيل الدخول"),
    "logout": MessageLookupByLibrary.simpleMessage("تسجيل الخروج"),
    "maintenance": MessageLookupByLibrary.simpleMessage("الصيانة"),
    "meetings": MessageLookupByLibrary.simpleMessage("الاجتماعات"),
    "mobile": MessageLookupByLibrary.simpleMessage("الجوال"),
    "monthlyStats": MessageLookupByLibrary.simpleMessage("إحصائيات الشهر"),
    "months": MessageLookupByLibrary.simpleMessage("أشهر"),
    "myArchivedTickets": MessageLookupByLibrary.simpleMessage(
      "تذاكري المؤرشفة",
    ),
    "myProfile": MessageLookupByLibrary.simpleMessage("ملفي"),
    "mySubscriptions": MessageLookupByLibrary.simpleMessage("اشتراكاتي"),
    "myTickets": MessageLookupByLibrary.simpleMessage("تذاكري"),
    "nearExpire": MessageLookupByLibrary.simpleMessage("أوشكت على الانتهاء"),
    "nearToExpire": MessageLookupByLibrary.simpleMessage("أوشكت على الانتهاء"),
    "newReplyOnTicket": MessageLookupByLibrary.simpleMessage(
      "رد جديد على التذكرة",
    ),
    "next": MessageLookupByLibrary.simpleMessage("التالي"),
    "noDataFound": MessageLookupByLibrary.simpleMessage("لا توجد بيانات"),
    "noProductsAvailable": MessageLookupByLibrary.simpleMessage(
      "لا توجد منتجات متاحة",
    ),
    "noRepliesFound": MessageLookupByLibrary.simpleMessage(
      "لم يتم العثور على ردود",
    ),
    "noSubProductsSelected": MessageLookupByLibrary.simpleMessage(
      "لم يتم اختيار منتجات فرعية",
    ),
    "note": MessageLookupByLibrary.simpleMessage("ملاحظة"),
    "noteRequired": MessageLookupByLibrary.simpleMessage("الملاحظة مطلوبة"),
    "of1": MessageLookupByLibrary.simpleMessage("من"),
    "officialHolidays": MessageLookupByLibrary.simpleMessage("عطل رسمية"),
    "password": MessageLookupByLibrary.simpleMessage("كلمة المرور"),
    "paymentDetails": MessageLookupByLibrary.simpleMessage("تفاصيل الدفع"),
    "paymentEvery": MessageLookupByLibrary.simpleMessage("الدفع كل"),
    "paymentInstallments": MessageLookupByLibrary.simpleMessage("أقساط الدفع"),
    "paymentStartDate": MessageLookupByLibrary.simpleMessage(
      "تاريخ بداية الدفع",
    ),
    "pdfGeneratedSuccessfully": MessageLookupByLibrary.simpleMessage(
      "تم إنشاء ملف PDF بنجاح",
    ),
    "pending": MessageLookupByLibrary.simpleMessage("قيد الانتظار"),
    "pickImage": MessageLookupByLibrary.simpleMessage("اختر صورة"),
    "pleaseCompleteAllSteps": MessageLookupByLibrary.simpleMessage(
      "يرجى إكمال جميع الخطوات",
    ),
    "pleaseSelectAtLeastOneProduct": MessageLookupByLibrary.simpleMessage(
      "يرجى اختيار منتج واحد على الأقل",
    ),
    "pleaseSelectAtLeastOneSubProduct": MessageLookupByLibrary.simpleMessage(
      "يرجى اختيار منتج فرعي واحد على الأقل",
    ),
    "pleaseSelectClientFirst": MessageLookupByLibrary.simpleMessage(
      "يرجى اختيار العميل أولاً",
    ),
    "previous": MessageLookupByLibrary.simpleMessage("السابق"),
    "price": MessageLookupByLibrary.simpleMessage("السعر"),
    "priceCalculation": MessageLookupByLibrary.simpleMessage("قييمة العرض"),
    "pricing": MessageLookupByLibrary.simpleMessage("تسعير"),
    "pricingSavedSuccessfully": MessageLookupByLibrary.simpleMessage(
      "تم حفظ التسعير بنجاح",
    ),
    "print": MessageLookupByLibrary.simpleMessage("طباعة"),
    "productId": MessageLookupByLibrary.simpleMessage("رقم المنتج"),
    "productSelected": MessageLookupByLibrary.simpleMessage("منتج محدد"),
    "productSelection": MessageLookupByLibrary.simpleMessage("اختيار المنتج"),
    "productsSelected": MessageLookupByLibrary.simpleMessage("منتجات محددة"),
    "profile": MessageLookupByLibrary.simpleMessage("الملف الشخصي"),
    "profilePicture": MessageLookupByLibrary.simpleMessage("الصورة الشخصية"),
    "profileUpdatedSuccessfully": MessageLookupByLibrary.simpleMessage(
      "تم تحديث الملف الشخصي بنجاح",
    ),
    "quotationCode": MessageLookupByLibrary.simpleMessage("رمز عرض السعر"),
    "quotationDetails": MessageLookupByLibrary.simpleMessage(
      "تفاصيل عرض السعر",
    ),
    "quotationPricing": MessageLookupByLibrary.simpleMessage("تسعير عرض السعر"),
    "quotationSaved": MessageLookupByLibrary.simpleMessage(
      "تم حفظ عرض السعر بنجاح",
    ),
    "quotationStatus": MessageLookupByLibrary.simpleMessage("حالة عرض السعر"),
    "quotationSummary": MessageLookupByLibrary.simpleMessage("ملخص عرض السعر"),
    "quotationSummaryNote": MessageLookupByLibrary.simpleMessage(
      "يرجى مراجعة جميع المعلومات أعلاه قبل إرسال عرض السعر. بمجرد الإرسال، يمكنك تعديل التسعير والتفاصيل الأخرى لاحقاً.",
    ),
    "quotationUpdatedSuccessfully": MessageLookupByLibrary.simpleMessage(
      "تم تحديث عرض السعر بنجاح",
    ),
    "quotations": MessageLookupByLibrary.simpleMessage("عروض الأسعار"),
    "quotationsList": MessageLookupByLibrary.simpleMessage("عروض الأسعار"),
    "reason": MessageLookupByLibrary.simpleMessage("السبب"),
    "reasonCannotBeEmpty": MessageLookupByLibrary.simpleMessage(
      "لا يمكن أن يكون السبب فارغًا",
    ),
    "recentActiveTickets": MessageLookupByLibrary.simpleMessage(
      "التذاكر النشطة الأخيرة",
    ),
    "register": MessageLookupByLibrary.simpleMessage("تسجيل"),
    "rejected": MessageLookupByLibrary.simpleMessage("مرفوضة"),
    "remainingDays": MessageLookupByLibrary.simpleMessage("الأيام المتبقية"),
    "remainingMaintenance": MessageLookupByLibrary.simpleMessage(
      "الصيانة المتبقية",
    ),
    "repliedOnTheTicket": MessageLookupByLibrary.simpleMessage(
      "رد على التذكرة",
    ),
    "replies": MessageLookupByLibrary.simpleMessage("الردود"),
    "reply": MessageLookupByLibrary.simpleMessage("رد"),
    "replyCannotBeEmpty": MessageLookupByLibrary.simpleMessage(
      "لا يمكن أن يكون الرد فارغًا",
    ),
    "replySentSuccessfully": MessageLookupByLibrary.simpleMessage(
      "تم إرسال الرد بنجاح",
    ),
    "reports": MessageLookupByLibrary.simpleMessage("التقارير"),
    "request": MessageLookupByLibrary.simpleMessage("طلب"),
    "requestLeave": MessageLookupByLibrary.simpleMessage("إجازة مطلوبة"),
    "requestLeaves": MessageLookupByLibrary.simpleMessage("إجازات مطلوبة"),
    "responsibleEmail": MessageLookupByLibrary.simpleMessage("ايميل المسؤول"),
    "responsibleJob": MessageLookupByLibrary.simpleMessage("وظيفة المسؤول"),
    "responsibleName": MessageLookupByLibrary.simpleMessage("اسم المسؤول"),
    "responsibleNameCannotBeEmpty": MessageLookupByLibrary.simpleMessage(
      "لا يمكن أن يكون اسم المسؤول فارغًا",
    ),
    "responsiblePhone": MessageLookupByLibrary.simpleMessage("رقم المسؤول"),
    "responsiblePhoneCannotBeEmpty": MessageLookupByLibrary.simpleMessage(
      "لا يمكن أن يكون رقم المسؤول فارغًا",
    ),
    "retry": MessageLookupByLibrary.simpleMessage("إعادة المحاولة"),
    "sales": MessageLookupByLibrary.simpleMessage("المبيعات"),
    "save": MessageLookupByLibrary.simpleMessage("حفظ"),
    "savePricing": MessageLookupByLibrary.simpleMessage("حفظ التسعير"),
    "saveQuotation": MessageLookupByLibrary.simpleMessage("حفظ عرض سعر"),
    "search": MessageLookupByLibrary.simpleMessage("بحث"),
    "searchByCodeOrTitle": MessageLookupByLibrary.simpleMessage(
      "البحث بالكود أو الشركة",
    ),
    "selectClient": MessageLookupByLibrary.simpleMessage("اختر العميل"),
    "selectDate": MessageLookupByLibrary.simpleMessage("اختر التاريخ"),
    "selectLeaveType": MessageLookupByLibrary.simpleMessage("اختر نوع الإجازة"),
    "selectParentProduct": MessageLookupByLibrary.simpleMessage(
      "اختر المنتج الرئيسي",
    ),
    "selectPrintLanguage": MessageLookupByLibrary.simpleMessage(
      "اختر لغة الطباعة",
    ),
    "selectProduct": MessageLookupByLibrary.simpleMessage("اختر المنتج"),
    "selectProductsAndServices": MessageLookupByLibrary.simpleMessage(
      "اختر المنتجات والخدمات",
    ),
    "selectSubProduct": MessageLookupByLibrary.simpleMessage(
      "اختر المنتج الفرعي",
    ),
    "selectSubProducts": MessageLookupByLibrary.simpleMessage(
      "اختر المنتجات الفرعية",
    ),
    "selectSubServices": MessageLookupByLibrary.simpleMessage(
      "اختر الخدمات الفرعية",
    ),
    "selectedClient": MessageLookupByLibrary.simpleMessage("العميل المختار"),
    "selectedProducts": MessageLookupByLibrary.simpleMessage(
      "المنتجات المختارة",
    ),
    "send": MessageLookupByLibrary.simpleMessage("إرسال"),
    "serviceName": MessageLookupByLibrary.simpleMessage("اسم الخدمة"),
    "services": MessageLookupByLibrary.simpleMessage("الخدمات"),
    "servicesPricing": MessageLookupByLibrary.simpleMessage("تسعير الخدمات"),
    "servicesTab": MessageLookupByLibrary.simpleMessage("الخدمات/المنتجات"),
    "settings": MessageLookupByLibrary.simpleMessage("الإعدادات"),
    "sickLeave": MessageLookupByLibrary.simpleMessage("إجازة مرضية"),
    "sickLeaves": MessageLookupByLibrary.simpleMessage("إجازات مرضية"),
    "softwareManagement": MessageLookupByLibrary.simpleMessage("إدارة البرامج"),
    "somethingWentWrong": MessageLookupByLibrary.simpleMessage("حدث خطأ ما"),
    "sortByDate": MessageLookupByLibrary.simpleMessage("ترتيب حسب التاريخ"),
    "startDate": MessageLookupByLibrary.simpleMessage("تاريخ البداية"),
    "status": MessageLookupByLibrary.simpleMessage("الحالة"),
    "statusHistory": MessageLookupByLibrary.simpleMessage("تاريخ الحالة"),
    "statusUpdatedSuccessfully": MessageLookupByLibrary.simpleMessage(
      "تم تحديث الحالة بنجاح",
    ),
    "step": MessageLookupByLibrary.simpleMessage("خطوة"),
    "subProductId": MessageLookupByLibrary.simpleMessage("رقم المنتج الفرعي"),
    "subProductSelection": MessageLookupByLibrary.simpleMessage(
      "اختيار المنتجات الفرعية",
    ),
    "subProducts": MessageLookupByLibrary.simpleMessage("المنتجات الفرعية"),
    "subServiceName": MessageLookupByLibrary.simpleMessage(
      "اسم الخدمة الفرعية",
    ),
    "subServiceSelected": MessageLookupByLibrary.simpleMessage(
      "خدمة فرعية محددة",
    ),
    "subServices": MessageLookupByLibrary.simpleMessage("الخدمات الفرعية"),
    "subServicesSelected": MessageLookupByLibrary.simpleMessage(
      "خدمات فرعية محددة",
    ),
    "submit": MessageLookupByLibrary.simpleMessage("إرسال"),
    "subscriptions": MessageLookupByLibrary.simpleMessage("الاشتراكات"),
    "subtotal": MessageLookupByLibrary.simpleMessage("المجموع الفرعي"),
    "summary": MessageLookupByLibrary.simpleMessage("ملخص"),
    "systemSetting": MessageLookupByLibrary.simpleMessage("إعداد النظام"),
    "theme": MessageLookupByLibrary.simpleMessage("المظهر"),
    "tickets": MessageLookupByLibrary.simpleMessage("التذاكر"),
    "toDate": MessageLookupByLibrary.simpleMessage("إلى تاريخ"),
    "toDateCannotBeEmpty": MessageLookupByLibrary.simpleMessage(
      "لا يمكن أن يكون تاريخ النهاية فارغًا",
    ),
    "total": MessageLookupByLibrary.simpleMessage("الإجمالي"),
    "totalTickets": MessageLookupByLibrary.simpleMessage("إجمالي التذاكر"),
    "updateProfile": MessageLookupByLibrary.simpleMessage("تحديث الملف الشخصي"),
    "updateQuotation": MessageLookupByLibrary.simpleMessage("تحديث عرض السعر"),
    "updateStatus": MessageLookupByLibrary.simpleMessage("تحديث الحالة"),
    "userEmail": MessageLookupByLibrary.simpleMessage("<EMAIL>"),
    "userLimit": MessageLookupByLibrary.simpleMessage("عدد المستخدمين"),
    "userName": MessageLookupByLibrary.simpleMessage("اسم المستخدم"),
    "username": MessageLookupByLibrary.simpleMessage("اسم المستخدم"),
    "users": MessageLookupByLibrary.simpleMessage("مستخدمين"),
    "vacationLeave": MessageLookupByLibrary.simpleMessage("إجازة اعتيادية"),
    "vacationLeaves": MessageLookupByLibrary.simpleMessage("إجازات اعتيادية"),
    "validityDays": MessageLookupByLibrary.simpleMessage("أيام الصلاحية"),
    "vat": MessageLookupByLibrary.simpleMessage("ضريبة القيمة المضافة"),
    "welcomeBack": MessageLookupByLibrary.simpleMessage("مرحبًا بعودتك"),
    "welcomeBackLine": MessageLookupByLibrary.simpleMessage("مرحبًا\nبعودتك"),
    "welcomeWithName": m0,
    "workTime": MessageLookupByLibrary.simpleMessage("وقت العمل"),
  };
}
