import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:opti_tickets/src/core/shared/extensions/context_extensions.dart';
import 'package:opti_tickets/src/core/theme/color_manager.dart';
import 'package:opti_tickets/src/screens/clients/models/client_model.dart';
import 'package:opti_tickets/src/screens/quotations/view/quotation_details_screen.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../core/shared/models/product_model.dart';
import '../../../core/shared/providers/product_providers.dart';
import '../models/quotation_model.dart';
import '../providers/quotation_providers.dart';

class EditQuotationStepsScreen extends HookConsumerWidget {
  final QuotationDetailData quotationData;

  const EditQuotationStepsScreen({
    super.key,
    required this.quotationData,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Start from step 1 (product selection) since client is already selected
    final currentStep = useState(1);
    final selectedClient = useState<Client?>(Client(
      clientID: quotationData.client.cid,
      clientname: quotationData.client.cName,
      cid: quotationData.client.cid,
      cName: quotationData.client.cName,
      cPerson: quotationData.client.cPerson,
      cAddress: quotationData.client.cAddress,
    ));

    // Pre-populate with existing data
    final selectedProducts = useState<List<Product>>([]);
    final selectedProductDetails = useState<List<SelectedProductDetail>>([]);
    final searchController = useTextEditingController();
    final searchQuery = useState('');
    final userLimitControllers = useState<Map<int, TextEditingController>>({});

    const totalSteps = 3; // Only 3 steps for edit mode

    // Initialize with existing quotation data
    useEffect(() {
      _initializeExistingData(
        selectedProducts,
        selectedProductDetails,
        userLimitControllers,
      );
      return null;
    }, []);

    return Scaffold(
      backgroundColor: ColorManager.lightGreyBackground,
      appBar: AppBar(
        title: Text(context.tr.editQuotation),
        backgroundColor: ColorManager.primaryColor,
        foregroundColor: Colors.white,
        leading: IconButton(
          onPressed: () => Navigator.pop(context),
          icon: const Icon(Icons.arrow_back_ios_new, color: Colors.white),
        ),
      ),
      body: SafeArea(
        child: Column(
          children: [
            // Step Indicator
            _buildStepIndicator(context, currentStep.value, totalSteps),

            // Step Content
            Expanded(
              child: _buildStepContent(
                context,
                ref,
                currentStep.value,
                selectedClient,
                selectedProducts,
                selectedProductDetails,
                searchController,
                searchQuery,
                userLimitControllers,
              ),
            ),

            // Navigation Buttons
            _buildNavigationButtons(
              context,
              ref,
              currentStep,
              totalSteps,
              selectedClient,
              selectedProducts,
              selectedProductDetails,
              userLimitControllers,
            ),
          ],
        ),
      ),
    );
  }

  void _initializeExistingData(
    ValueNotifier<List<Product>> selectedProducts,
    ValueNotifier<List<SelectedProductDetail>> selectedProductDetails,
    ValueNotifier<Map<int, TextEditingController>> userLimitControllers,
  ) {
    // Convert existing quotation services to products and product details
    List<Product> products = [];
    List<SelectedProductDetail> productDetails = [];
    Map<int, TextEditingController> controllers = {};

    for (final service in quotationData.subs) {
      // Create product from service
      final product = Product(
        productId: service.serviceId,
        productName: service.serviceName,
        hasSubList: service.subservices.isNotEmpty ? 1 : 0,
        hasLimitUser: service.serviceHasLimitUser ? 1 : 0,
        subProducts: service.subservices
            .map((sub) => SubProduct(
                  subProductId: sub.subserviceId,
                  subProductName: sub.subserviceName,
                  subProductCost: sub.subserviceCost,
                ))
            .toList(),
      );
      products.add(product);

      // Create product detail
      final selectedSubProducts = service.subservices
          .map((sub) => SubProduct(
                subProductId: sub.subserviceId,
                subProductName: sub.subserviceName,
                subProductCost: sub.subserviceCost,
              ))
          .toList();

      final productDetail = SelectedProductDetail(
        product: product,
        selectedSubProducts: selectedSubProducts,
        userLimit: service.serviceHasLimitUser
            ? int.tryParse(service.limitUser) ?? 0
            : 0,
      );
      productDetails.add(productDetail);

      // Create controller for user limit if needed
      if (service.serviceHasLimitUser) {
        controllers[service.serviceId] =
            TextEditingController(text: service.limitUser);
      }
    }

    selectedProducts.value = products;
    selectedProductDetails.value = productDetails;
    userLimitControllers.value = controllers;
  }

  Widget _buildStepIndicator(
      BuildContext context, int currentStep, int totalSteps) {
    return Container(
      padding: const EdgeInsets.all(AppSpaces.padding16),
      color: Colors.white,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: List.generate(totalSteps * 2 - 1, (index) {
          if (index.isOdd) {
            // This is a connector line
            final stepIndex = index ~/ 2;
            final isCompleted = stepIndex < currentStep - 1;
            return Expanded(
              child: Container(
                height: 2,
                margin: const EdgeInsets.symmetric(horizontal: 4),
                color: isCompleted
                    ? ColorManager.primaryColor
                    : ColorManager.lightGrey,
              ),
            );
          } else {
            // This is a step circle
            final stepIndex = index ~/ 2;
            final isCompleted = stepIndex < currentStep - 1;
            final isCurrent = stepIndex == currentStep - 1;

            return Container(
              width: 32,
              height: 32,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: isCompleted || isCurrent
                    ? ColorManager.primaryColor
                    : ColorManager.lightGrey,
                border: Border.all(
                  color: ColorManager.primaryColor,
                  width: 2,
                ),
              ),
              child: Center(
                child: isCompleted
                    ? const Icon(
                        Icons.check,
                        color: Colors.white,
                        size: 16,
                      )
                    : Text(
                        '${stepIndex + 2}', // Start from step 2 since we skip client selection
                        style: AppTextStyles.labelSmall.copyWith(
                          color: isCurrent
                              ? Colors.white
                              : ColorManager.primaryColor,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
              ),
            );
          }
        }),
      ),
    );
  }

  Widget _buildStepContent(
    BuildContext context,
    WidgetRef ref,
    int currentStep,
    ValueNotifier<Client?> selectedClient,
    ValueNotifier<List<Product>> selectedProducts,
    ValueNotifier<List<SelectedProductDetail>> selectedProductDetails,
    TextEditingController searchController,
    ValueNotifier<String> searchQuery,
    ValueNotifier<Map<int, TextEditingController>> userLimitControllers,
  ) {
    switch (currentStep) {
      case 1:
        return _buildProductSelectionStep(
          context,
          ref,
          selectedProducts,
          selectedProductDetails,
          searchController,
          searchQuery,
          userLimitControllers,
        );
      case 2:
        return _buildSubProductSelectionStep(
          context,
          selectedProducts,
          selectedProductDetails,
          userLimitControllers,
        );
      case 3:
        return _buildConfirmationStep(
          context,
          selectedClient,
          selectedProducts,
          selectedProductDetails,
          userLimitControllers,
        );
      default:
        return const SizedBox.shrink();
    }
  }

  Widget _buildNavigationButtons(
    BuildContext context,
    WidgetRef ref,
    ValueNotifier<int> currentStep,
    int totalSteps,
    ValueNotifier<Client?> selectedClient,
    ValueNotifier<List<Product>> selectedProducts,
    ValueNotifier<List<SelectedProductDetail>> selectedProductDetails,
    ValueNotifier<Map<int, TextEditingController>> userLimitControllers,
  ) {
    return Container(
      padding: const EdgeInsets.all(AppSpaces.padding16),
      color: Colors.white,
      child: Row(
        children: [
          if (currentStep.value > 1)
            Expanded(
              child: Button(
                label: context.tr.previous,
                onPressed: () {
                  currentStep.value--;
                },
                color: ColorManager.lightGrey,
                textColor: ColorManager.darkGrey,
              ),
            ),
          if (currentStep.value > 1) AppGaps.gap16,
          Expanded(
            child: Button(
              label: currentStep.value == totalSteps
                  ? context.tr.updateQuotation
                  : context.tr.next,
              onPressed: () => _handleNextStep(
                context,
                ref,
                currentStep,
                totalSteps,
                selectedClient,
                selectedProducts,
                selectedProductDetails,
                userLimitControllers,
              ),
              color: ColorManager.primaryColor,
            ),
          ),
        ],
      ),
    );
  }

  void _handleNextStep(
    BuildContext context,
    WidgetRef ref,
    ValueNotifier<int> currentStep,
    int totalSteps,
    ValueNotifier<Client?> selectedClient,
    ValueNotifier<List<Product>> selectedProducts,
    ValueNotifier<List<SelectedProductDetail>> selectedProductDetails,
    ValueNotifier<Map<int, TextEditingController>> userLimitControllers,
  ) {
    if (currentStep.value < totalSteps) {
      currentStep.value++;
    } else {
      _updateQuotation(
        context,
        ref,
        selectedClient,
        selectedProducts,
        selectedProductDetails,
        userLimitControllers,
      );
    }
  }

  void _updateQuotation(
    BuildContext context,
    WidgetRef ref,
    ValueNotifier<Client?> selectedClient,
    ValueNotifier<List<Product>> selectedProducts,
    ValueNotifier<List<SelectedProductDetail>> selectedProductDetails,
    ValueNotifier<Map<int, TextEditingController>> userLimitControllers,
  ) async {
    if (selectedClient.value == null || selectedProductDetails.value.isEmpty) {
      context.showBarMessage(
        context.tr.pleaseCompleteAllSteps,
        isError: true,
      );
      return;
    }

    try {
      final quotationController = ref.read(quotationControllerNotifierProvider);

      // Create update model with quotation_id
      final updateModel = UpdateQuotationModel(
        qInfo: QuotationUpdateInfo(
          clientID: selectedClient.value!.cid.toString(),
          quotationId: quotationData.ipd.toString(),
          createdDate: DateFormat('yyyy-MM-dd').format(DateTime.now()),
          validityDays: '7',
        ),
        qDetails: selectedProductDetails.value.expand((detail) {
          if (detail.selectedSubProducts.isEmpty) {
            // Product without sub-products
            return [
              QuotationDetail(
                productId: detail.product.productId.toString(),
                subProductId: detail.product.productId.toString(),
                hasLimitUsers: detail.userLimit,
              )
            ];
          } else {
            // Product with sub-products
            return detail.selectedSubProducts.map((subProduct) {
              return QuotationDetail(
                productId: detail.product.productId.toString(),
                subProductId: subProduct.subProductId.toString(),
                hasLimitUsers: detail.userLimit,
              );
            });
          }
        }).toList(),
        task: QuotationTask(
          startDate: DateFormat('yyyy-MM-dd').format(DateTime.now()),
          startTime: DateFormat('HH:mm').format(DateTime.now()),
          finishDate: DateFormat('yyyy-MM-dd').format(DateTime.now()),
          finishTime: DateFormat('HH:mm').format(DateTime.now()),
        ),
      );

      final result = await quotationController.updateQuotation(updateModel);

      if (result != null && context.mounted) {
        context.showBarMessage(context.tr.quotationUpdatedSuccessfully);
        Navigator.pushReplacement(
          context,
          MaterialPageRoute(
            builder: (context) => QuotationDetailsScreen(
              quotationId: quotationData.ipd,
            ),
          ),
        );
      } else if (context.mounted) {
        context.showBarMessage(
          context.tr.failedToUpdateQuotation,
          isError: true,
        );
      }
    } catch (e) {
      if (context.mounted) {
        context.showBarMessage(
          context.tr.failedToUpdateQuotation,
          isError: true,
        );
      }
    }
  }

  Widget _buildProductSelectionStep(
    BuildContext context,
    WidgetRef ref,
    ValueNotifier<List<Product>> selectedProducts,
    ValueNotifier<List<SelectedProductDetail>> selectedProductDetails,
    TextEditingController searchController,
    ValueNotifier<String> searchQuery,
    ValueNotifier<Map<int, TextEditingController>> userLimitControllers,
  ) {
    final productsAsyncValue = ref
        .watch(getProductsListByClientFutureProvider(quotationData.client.cid));

    return Padding(
      padding: const EdgeInsets.all(AppSpaces.padding16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '${context.tr.step} 2 ${context.tr.of1} 3',
            style: AppTextStyles.labelLarge.copyWith(
              color: ColorManager.darkGrey,
            ),
          ),
          AppGaps.gap8,
          Text(
            context.tr.productSelection,
            style: AppTextStyles.title.copyWith(
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          AppGaps.gap16,
          Text(
            context.tr.selectProductsAndServices,
            style: AppTextStyles.labelLarge.copyWith(
              color: ColorManager.darkGrey,
            ),
          ),
          AppGaps.gap16,
          Expanded(
            child: productsAsyncValue.when(
              data: (productList) {
                if (!productList.success ||
                    productList.data.categoryLists.isEmpty) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(
                          Icons.inventory_2_outlined,
                          size: 64,
                          color: ColorManager.lightGrey,
                        ),
                        AppGaps.gap16,
                        Text(
                          context.tr.noProductsAvailable,
                          style: AppTextStyles.body.copyWith(
                            color: ColorManager.darkGrey,
                          ),
                        ),
                      ],
                    ),
                  );
                }

                return GridView.builder(
                  gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 2,
                    crossAxisSpacing: 12,
                    mainAxisSpacing: 12,
                    childAspectRatio: 0.8,
                  ),
                  itemCount: productList.data.categoryLists.length,
                  itemBuilder: (context, index) {
                    final product = productList.data.categoryLists[index];
                    final isSelected = selectedProducts.value
                        .any((p) => p.productId == product.productId);

                    // Get or create user limit controller
                    final controller =
                        userLimitControllers.value[product.productId] ??
                            TextEditingController();
                    if (!userLimitControllers.value
                        .containsKey(product.productId)) {
                      userLimitControllers.value[product.productId] =
                          controller;
                    }

                    return GestureDetector(
                      onTap: () {
                        final updatedProducts =
                            List<Product>.from(selectedProducts.value);

                        if (!isSelected) {
                          if (!updatedProducts
                              .any((p) => p.productId == product.productId)) {
                            updatedProducts.add(product);
                          }
                        } else {
                          updatedProducts.removeWhere(
                              (p) => p.productId == product.productId);
                          // Clear user limit when deselected
                          controller.clear();
                        }

                        selectedProducts.value = updatedProducts;
                      },
                      child: Card(
                        elevation: isSelected ? 4 : 1,
                        shape: RoundedRectangleBorder(
                          borderRadius:
                              BorderRadius.circular(AppRadius.radius12),
                          side: BorderSide(
                            color: isSelected
                                ? ColorManager.primaryColor
                                : Colors.transparent,
                            width: 2,
                          ),
                        ),
                        child: Padding(
                          padding: const EdgeInsets.all(AppSpaces.padding12),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              // Product Icon/Image
                              Container(
                                width: double.infinity,
                                height: 60,
                                decoration: BoxDecoration(
                                  color: isSelected
                                      ? ColorManager.primaryColor
                                          .withOpacity(0.1)
                                      : ColorManager.lightGrey.withOpacity(0.3),
                                  borderRadius:
                                      BorderRadius.circular(AppRadius.radius8),
                                ),
                                child: Icon(
                                  Icons.inventory_2,
                                  size: 32,
                                  color: isSelected
                                      ? ColorManager.primaryColor
                                      : ColorManager.darkGrey,
                                ),
                              ),
                              AppGaps.gap8,
                              // Product Name
                              Text(
                                product.productName,
                                style: AppTextStyles.labelMedium.copyWith(
                                  fontWeight: FontWeight.w600,
                                  color: isSelected
                                      ? ColorManager.primaryColor
                                      : ColorManager.darkGrey,
                                ),
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                              ),
                              AppGaps.gap8,
                              // User Limit Input (if applicable)
                              if (product.hasUserLimit)
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      context.tr.userLimit,
                                      style: AppTextStyles.labelSmall.copyWith(
                                        color: ColorManager.darkGrey,
                                      ),
                                    ),
                                    AppGaps.gap4,
                                    TextField(
                                      controller: controller,
                                      keyboardType: TextInputType.number,
                                      decoration: InputDecoration(
                                        hintText: context.tr.enterUserLimit,
                                        border: OutlineInputBorder(
                                          borderRadius: BorderRadius.circular(
                                              AppRadius.radius8),
                                        ),
                                        contentPadding:
                                            const EdgeInsets.symmetric(
                                          horizontal: 8,
                                          vertical: 4,
                                        ),
                                      ),
                                      style: AppTextStyles.labelSmall,
                                    ),
                                  ],
                                ),
                            ],
                          ),
                        ),
                      ),
                    );
                  },
                );
              },
              loading: () => const Center(child: CircularProgressIndicator()),
              error: (error, stackTrace) => Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(
                      Icons.error_outline,
                      size: 64,
                      color: ColorManager.errorColor,
                    ),
                    AppGaps.gap16,
                    Text(
                      context.tr.failedToLoadProducts,
                      style: AppTextStyles.body.copyWith(
                        color: ColorManager.errorColor,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSubProductSelectionStep(
    BuildContext context,
    ValueNotifier<List<Product>> selectedProducts,
    ValueNotifier<List<SelectedProductDetail>> selectedProductDetails,
    ValueNotifier<Map<int, TextEditingController>> userLimitControllers,
  ) {
    return Padding(
      padding: const EdgeInsets.all(AppSpaces.padding16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '${context.tr.step} 3 ${context.tr.of1} 3',
            style: AppTextStyles.labelLarge.copyWith(
              color: ColorManager.darkGrey,
            ),
          ),
          AppGaps.gap8,
          Text(
            context.tr.subProductSelection,
            style: AppTextStyles.title.copyWith(
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          AppGaps.gap16,
          Text(
            context.tr.selectSubProducts,
            style: AppTextStyles.labelLarge.copyWith(
              color: ColorManager.darkGrey,
            ),
          ),
          AppGaps.gap16,
          Expanded(
            child: selectedProducts.value.isEmpty
                ? Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(
                          Icons.inventory_2_outlined,
                          size: 64,
                          color: ColorManager.lightGrey,
                        ),
                        AppGaps.gap16,
                        Text(
                          context.tr.pleaseSelectAtLeastOneProduct,
                          style: AppTextStyles.body.copyWith(
                            color: ColorManager.darkGrey,
                          ),
                        ),
                      ],
                    ),
                  )
                : ListView.separated(
                    itemCount: selectedProducts.value.length,
                    separatorBuilder: (context, index) => AppGaps.gap16,
                    itemBuilder: (context, index) {
                      final product = selectedProducts.value[index];

                      // Find existing product detail or create new one
                      final existingDetail = selectedProductDetails.value
                          .where((detail) =>
                              detail.product.productId == product.productId)
                          .firstOrNull;

                      final selectedSubProducts =
                          existingDetail?.selectedSubProducts ?? <SubProduct>[];

                      return Card(
                        elevation: 2,
                        shape: RoundedRectangleBorder(
                          borderRadius:
                              BorderRadius.circular(AppRadius.radius12),
                        ),
                        child: Padding(
                          padding: const EdgeInsets.all(AppSpaces.padding16),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                product.productName,
                                style: AppTextStyles.subTitle.copyWith(
                                  fontWeight: FontWeight.w600,
                                  color: ColorManager.primaryColor,
                                ),
                              ),
                              AppGaps.gap12,
                              if (product.hasSubList == 1 &&
                                  product.subProducts.isNotEmpty)
                                ...product.subProducts.map((subProduct) {
                                  final isSelected = selectedSubProducts.any(
                                      (sub) =>
                                          sub.subProductId ==
                                          subProduct.subProductId);

                                  return CheckboxListTile(
                                    dense: true,
                                    contentPadding: EdgeInsets.zero,
                                    title: Text(
                                      subProduct.subProductName,
                                      style: AppTextStyles.body,
                                    ),
                                    value: isSelected,
                                    onChanged: (value) {
                                      _updateSubProductSelection(
                                        selectedProductDetails,
                                        product,
                                        subProduct,
                                        value ?? false,
                                      );
                                    },
                                  );
                                })
                              else
                                Padding(
                                  padding: const EdgeInsets.symmetric(
                                      vertical: AppSpaces.padding8),
                                  child: Text(
                                    '${context.tr.done} - ${context.tr.noProductsAvailable}',
                                    style: AppTextStyles.labelLarge.copyWith(
                                      color: ColorManager.darkGrey,
                                      fontStyle: FontStyle.italic,
                                    ),
                                  ),
                                ),
                            ],
                          ),
                        ),
                      );
                    },
                  ),
          ),
        ],
      ),
    );
  }

  void _updateSubProductSelection(
    ValueNotifier<List<SelectedProductDetail>> selectedProductDetails,
    Product product,
    SubProduct subProduct,
    bool isSelected,
  ) {
    final updatedDetails =
        List<SelectedProductDetail>.from(selectedProductDetails.value);

    // Find existing detail for this product
    final existingIndex = updatedDetails.indexWhere(
      (detail) => detail.product.productId == product.productId,
    );

    if (existingIndex != -1) {
      // Update existing detail
      final existingDetail = updatedDetails[existingIndex];
      final updatedSubProducts =
          List<SubProduct>.from(existingDetail.selectedSubProducts);

      if (isSelected) {
        if (!updatedSubProducts
            .any((sub) => sub.subProductId == subProduct.subProductId)) {
          updatedSubProducts.add(subProduct);
        }
      } else {
        updatedSubProducts
            .removeWhere((sub) => sub.subProductId == subProduct.subProductId);
      }

      updatedDetails[existingIndex] = existingDetail.copyWith(
        selectedSubProducts: updatedSubProducts,
      );
    } else {
      // Create new detail
      if (isSelected) {
        updatedDetails.add(SelectedProductDetail(
          product: product,
          selectedSubProducts: [subProduct],
          userLimit: 0,
        ));
      }
    }

    selectedProductDetails.value = updatedDetails;
  }

  Widget _buildConfirmationStep(
    BuildContext context,
    ValueNotifier<Client?> selectedClient,
    ValueNotifier<List<Product>> selectedProducts,
    ValueNotifier<List<SelectedProductDetail>> selectedProductDetails,
    ValueNotifier<Map<int, TextEditingController>> userLimitControllers,
  ) {
    return Padding(
      padding: const EdgeInsets.all(AppSpaces.padding16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '${context.tr.step} 3 ${context.tr.of1} 3',
            style: AppTextStyles.labelLarge.copyWith(
              color: ColorManager.darkGrey,
            ),
          ),
          AppGaps.gap8,
          Text(
            context.tr.quotationSummary,
            style: AppTextStyles.title.copyWith(
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          AppGaps.gap16,
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                children: [
                  // Client Summary
                  if (selectedClient.value != null)
                    _buildClientSummaryCard(context, selectedClient.value!),
                  AppGaps.gap16,
                  // Products Summary
                  _buildProductsSummaryCard(
                    context,
                    selectedProducts.value,
                    selectedProductDetails.value,
                    userLimitControllers.value,
                  ),
                  AppGaps.gap16,
                  // Note
                  Card(
                    elevation: 2,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(AppRadius.radius12),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.all(AppSpaces.padding16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              const Icon(
                                Icons.info_outline,
                                color: ColorManager.primaryColor,
                                size: 20,
                              ),
                              AppGaps.gap8,
                              Text(
                                context.tr.note,
                                style: AppTextStyles.subTitle.copyWith(
                                  fontWeight: FontWeight.w600,
                                  color: ColorManager.primaryColor,
                                ),
                              ),
                            ],
                          ),
                          AppGaps.gap8,
                          Text(
                            context.tr.quotationSummaryNote,
                            style: AppTextStyles.body.copyWith(
                              color: ColorManager.darkGrey,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildClientSummaryCard(BuildContext context, Client client) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppRadius.radius12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(AppSpaces.padding16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(
                  Icons.business,
                  color: ColorManager.primaryColor,
                  size: 20,
                ),
                AppGaps.gap8,
                Text(
                  context.tr.selectedClient,
                  style: AppTextStyles.subTitle.copyWith(
                    fontWeight: FontWeight.w600,
                    color: ColorManager.primaryColor,
                  ),
                ),
              ],
            ),
            AppGaps.gap12,
            Text(
              client.cName,
              style: AppTextStyles.body.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProductsSummaryCard(
    BuildContext context,
    List<Product> selectedProducts,
    List<SelectedProductDetail> selectedProductDetails,
    Map<int, TextEditingController> userLimitControllers,
  ) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppRadius.radius12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(AppSpaces.padding16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(
                  Icons.inventory_2,
                  color: ColorManager.primaryColor,
                  size: 20,
                ),
                AppGaps.gap8,
                Text(
                  context.tr.selectedProducts,
                  style: AppTextStyles.subTitle.copyWith(
                    fontWeight: FontWeight.w600,
                    color: ColorManager.primaryColor,
                  ),
                ),
              ],
            ),
            AppGaps.gap12,
            ...selectedProducts.map((product) {
              final productDetail = selectedProductDetails
                  .where(
                      (detail) => detail.product.productId == product.productId)
                  .firstOrNull;
              final userLimit =
                  userLimitControllers[product.productId]?.text ?? '';

              return Padding(
                padding: const EdgeInsets.only(bottom: 12),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      product.productName,
                      style: AppTextStyles.body.copyWith(
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    if (product.hasUserLimit && userLimit.isNotEmpty)
                      Text(
                        '${context.tr.userLimit}: $userLimit ${context.tr.users}',
                        style: AppTextStyles.labelSmall.copyWith(
                          color: ColorManager.darkGrey,
                        ),
                      ),
                    if (productDetail != null &&
                        productDetail.selectedSubProducts.isNotEmpty)
                      ...productDetail.selectedSubProducts
                          .map((subProduct) => Padding(
                                padding:
                                    const EdgeInsets.only(left: 16, top: 4),
                                child: Text(
                                  '• ${subProduct.subProductName}',
                                  style: AppTextStyles.labelSmall.copyWith(
                                    color: ColorManager.darkGrey,
                                  ),
                                ),
                              ))
                    else if (product.hasSubList == 1)
                      Padding(
                        padding: const EdgeInsets.only(left: 16, top: 4),
                        child: Text(
                          context.tr.noSubProductsSelected,
                          style: AppTextStyles.labelSmall.copyWith(
                            color: ColorManager.darkGrey,
                            fontStyle: FontStyle.italic,
                          ),
                        ),
                      ),
                  ],
                ),
              );
            }),
          ],
        ),
      ),
    );
  }
}
