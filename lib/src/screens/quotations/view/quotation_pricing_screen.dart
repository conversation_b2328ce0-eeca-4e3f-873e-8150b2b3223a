import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:opti_tickets/src/core/shared/extensions/context_extensions.dart';
import 'package:opti_tickets/src/core/shared/widgets/app_bar/base_appbar.dart';
import 'package:opti_tickets/src/core/shared/widgets/loading/loading_widget.dart';
import 'package:opti_tickets/src/core/theme/color_manager.dart';
import 'package:opti_tickets/src/screens/quotations/models/quotation_model.dart';
import 'package:opti_tickets/src/screens/quotations/providers/quotation_providers.dart';
import 'package:opti_tickets/src/screens/quotations/view/quotation_details_screen.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../main_screen/view/main_screen.dart';

class QuotationPricingScreen extends HookConsumerWidget {
  final QuotationDetailData quotationData;

  const QuotationPricingScreen({
    super.key,
    required this.quotationData,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final pricingDetails = useState<List<QuotationPricingDetail>>([]);
    final paymentData = useState<QuotationPayment>(QuotationPayment(
      payStartDate: DateFormat('yyyy-MM-dd', 'en').format(DateTime.now()),
    ));
    final isLoading = useState(false);
    final dateController = useTextEditingController(
      text: DateFormat('yyyy-MM-dd', 'en').format(DateTime.now()),
    );

    // Initialize pricing details from quotation services
    useEffect(() {
      final details = quotationData.subs.map((service) {
        return QuotationPricingDetail(
          productId: service.serviceId.toString(),
          extraUserCost: service.extraUserCost.toString(),
          subproducts: service.subservices.map((subService) {
            return QuotationPricingSubProduct(
              subProductId: subService.subserviceId.toString(),
              cost: subService.subserviceCost,
            );
          }).toList(),
        );
      }).toList();

      pricingDetails.value = details;

      // Calculate initial amount
      final totalAmount = _calculateTotalAmount(details);
      paymentData.value = paymentData.value.copyWith(
        initialAmount: totalAmount.toString(),
      );

      return null;
    }, []);

    return Scaffold(
      backgroundColor: ColorManager.lightGreyBackground,
      appBar: BaseAppBar(
        title: context.tr.quotationPricing,
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(AppSpaces.padding16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header Card
              _buildHeaderCard(context),

              AppGaps.gap16,

              // Services Pricing
              _buildServicesPricingCard(context, pricingDetails, paymentData),

              AppGaps.gap16,

              // Payment Details
              _buildPaymentCard(context, paymentData, dateController),

              AppGaps.gap24,

              // Save Button
              Button(
                loadingWidget: const LoadingWidget(),
                label: context.tr.savePricing,
                onPressed: isLoading.value
                    ? null
                    : () => _savePricing(
                          context,
                          ref,
                          pricingDetails.value,
                          paymentData.value,
                          isLoading,
                        ),
                isLoading: isLoading.value,
                color: ColorManager.primaryColor,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeaderCard(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppRadius.radius12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(AppSpaces.padding16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              quotationData.unicode,
              style: AppTextStyles.title.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            AppGaps.gap8,
            Row(
              children: [
                const Icon(
                  Icons.business,
                  size: 16,
                  color: ColorManager.primaryColor,
                ),
                AppGaps.gap8,
                Text(
                  quotationData.client.cName,
                  style: AppTextStyles.body.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildServicesPricingCard(
    BuildContext context,
    ValueNotifier<List<QuotationPricingDetail>> pricingDetails,
    ValueNotifier<QuotationPayment> paymentData,
  ) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppRadius.radius12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(AppSpaces.padding16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              context.tr.servicesPricing,
              style: AppTextStyles.title.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            AppGaps.gap16,
            ...pricingDetails.value.asMap().entries.map((entry) {
              final index = entry.key;
              final detail = entry.value;
              final service = quotationData.subs[index];

              return _buildServicePricingItem(
                context,
                service,
                detail,
                index,
                pricingDetails,
                paymentData,
              );
            }),
          ],
        ),
      ),
    );
  }

  Widget _buildServicePricingItem(
    BuildContext context,
    QuotationService service,
    QuotationPricingDetail detail,
    int index,
    ValueNotifier<List<QuotationPricingDetail>> pricingDetails,
    ValueNotifier<QuotationPayment> paymentData,
  ) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: ColorManager.lightGreyBackground,
        borderRadius: BorderRadius.circular(AppRadius.radius8),
        border: Border.all(color: ColorManager.lightGrey),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            service.serviceName,
            style: AppTextStyles.subTitle.copyWith(
              fontWeight: FontWeight.w600,
              color: ColorManager.primaryColor,
            ),
          ),
          if (service.serviceHasLimitUser) ...[
            AppGaps.gap12,
            Text(
              '${context.tr.extraUserCost}:',
              style: AppTextStyles.body.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
            AppGaps.gap8,
            TextFormField(
              initialValue: detail.extraUserCost,
              keyboardType: TextInputType.number,
              decoration: InputDecoration(
                hintText: context.tr.enterCost,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(AppRadius.radius8),
                ),
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 8,
                ),
              ),
              onChanged: (value) {
                final updatedDetails =
                    List<QuotationPricingDetail>.from(pricingDetails.value);
                updatedDetails[index] = detail.copyWith(extraUserCost: value);
                pricingDetails.value = updatedDetails;

                // Update total amount
                final totalAmount = _calculateTotalAmount(updatedDetails);
                paymentData.value = paymentData.value.copyWith(
                  initialAmount: totalAmount.toString(),
                );
              },
            ),
          ],
          if (detail.subproducts.isNotEmpty) ...[
            AppGaps.gap12,
            Text(
              '${context.tr.subServices}:',
              style: AppTextStyles.body.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
            AppGaps.gap8,
            ...detail.subproducts.asMap().entries.map((subEntry) {
              final subIndex = subEntry.key;
              final subProduct = subEntry.value;
              final subService = service.subservices[subIndex];

              return Padding(
                padding: const EdgeInsets.only(bottom: 8),
                child: Row(
                  children: [
                    Expanded(
                      flex: 2,
                      child: Text(
                        subService.subserviceName,
                        style: AppTextStyles.body,
                      ),
                    ),
                    AppGaps.gap8,
                    Expanded(
                      child: TextFormField(
                        initialValue: subProduct.cost,
                        keyboardType: TextInputType.number,
                        decoration: InputDecoration(
                          hintText: context.tr.cost,
                          border: OutlineInputBorder(
                            borderRadius:
                                BorderRadius.circular(AppRadius.radius8),
                          ),
                          contentPadding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 4,
                          ),
                        ),
                        onChanged: (value) {
                          final updatedDetails =
                              List<QuotationPricingDetail>.from(
                                  pricingDetails.value);
                          final updatedSubProducts =
                              List<QuotationPricingSubProduct>.from(
                                  detail.subproducts);
                          updatedSubProducts[subIndex] =
                              subProduct.copyWith(cost: value);
                          updatedDetails[index] =
                              detail.copyWith(subproducts: updatedSubProducts);
                          pricingDetails.value = updatedDetails;

                          // Update total amount
                          final totalAmount =
                              _calculateTotalAmount(updatedDetails);
                          paymentData.value = paymentData.value.copyWith(
                            initialAmount: totalAmount.toString(),
                          );
                        },
                      ),
                    ),
                  ],
                ),
              );
            }),
          ],
        ],
      ),
    );
  }

  Widget _buildPaymentCard(
    BuildContext context,
    ValueNotifier<QuotationPayment> paymentData,
    TextEditingController dateController,
  ) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppRadius.radius12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(AppSpaces.padding16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              context.tr.paymentDetails,
              style: AppTextStyles.title.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            AppGaps.gap16,

            // Initial Amount (Auto-calculated)
            Text(
              '${context.tr.initialAmount}:',
              style: AppTextStyles.body.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
            AppGaps.gap8,
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: ColorManager.lightGreyBackground,
                borderRadius: BorderRadius.circular(AppRadius.radius8),
                border: Border.all(color: ColorManager.lightGrey),
              ),
              child: Text(
                paymentData.value.initialAmount,
                style: AppTextStyles.subTitle.copyWith(
                  fontWeight: FontWeight.w600,
                  color: ColorManager.primaryColor,
                ),
              ),
            ),

            AppGaps.gap16,

            // Discount
            Text(
              '${context.tr.discount}:',
              style: AppTextStyles.body.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
            AppGaps.gap8,
            TextFormField(
              initialValue: paymentData.value.discount,
              keyboardType: TextInputType.number,
              decoration: InputDecoration(
                hintText: context.tr.enterDiscount,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(AppRadius.radius8),
                ),
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 8,
                ),
              ),
              onChanged: (value) {
                paymentData.value = paymentData.value.copyWith(discount: value);
              },
            ),

            AppGaps.gap16,

            // Payment Installments
            Text(
              '${context.tr.paymentInstallments}:',
              style: AppTextStyles.body.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
            AppGaps.gap8,
            TextFormField(
              initialValue: paymentData.value.payInstallments,
              keyboardType: TextInputType.number,
              decoration: InputDecoration(
                hintText: context.tr.enterInstallments,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(AppRadius.radius8),
                ),
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 8,
                ),
              ),
              onChanged: (value) {
                paymentData.value =
                    paymentData.value.copyWith(payInstallments: value);
              },
            ),

            AppGaps.gap16,

            // Payment Start Date
            Text(
              '${context.tr.paymentStartDate}:',
              style: AppTextStyles.body.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
            AppGaps.gap8,
            TextFormField(
              controller: dateController,
              decoration: InputDecoration(
                hintText: 'YYYY-MM-DD',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(AppRadius.radius8),
                ),
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 8,
                ),
                suffixIcon: const Icon(Icons.calendar_today),
              ),
              onTap: () async {
                final date = await showDatePicker(
                  context: context,
                  initialDate: DateTime.now(),
                  firstDate: DateTime.now(),
                  lastDate: DateTime.now().add(const Duration(days: 365)),
                );
                if (date != null) {
                  final formattedDate =
                      DateFormat('yyyy-MM-dd', 'en').format(date);
                  dateController.text = formattedDate;
                  paymentData.value =
                      paymentData.value.copyWith(payStartDate: formattedDate);
                }
              },
              readOnly: true,
            ),

            AppGaps.gap16,

            // Payment Every (months)
            Text(
              '${context.tr.paymentEvery} (${context.tr.months}):',
              style: AppTextStyles.body.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
            AppGaps.gap8,
            TextFormField(
              initialValue: paymentData.value.payEvery,
              keyboardType: TextInputType.number,
              decoration: InputDecoration(
                hintText: context.tr.enterMonths,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(AppRadius.radius8),
                ),
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 8,
                ),
              ),
              onChanged: (value) {
                paymentData.value = paymentData.value.copyWith(payEvery: value);
              },
            ),
          ],
        ),
      ),
    );
  }

  double _calculateTotalAmount(List<QuotationPricingDetail> details) {
    double total = 0.0;
    for (final detail in details) {
      // Add extra user cost
      total += double.tryParse(detail.extraUserCost) ?? 0.0;

      // Add subproduct costs
      for (final subProduct in detail.subproducts) {
        total += double.tryParse(subProduct.cost) ?? 0.0;
      }
    }
    return total;
  }

  Future<void> _savePricing(
    BuildContext context,
    WidgetRef ref,
    List<QuotationPricingDetail> details,
    QuotationPayment payment,
    ValueNotifier<bool> isLoading,
  ) async {
    try {
      isLoading.value = true;

      final pricingModel = QuotationPricingModel(
        qcId: quotationData.ipd.toString(),
        qcDetails: details,
        qcPayment: payment,
      );

      final quotationController = ref.read(quotationControllerProvider);
      await quotationController.assignQuotationPrice(
          pricingModel: pricingModel);

      if (context.mounted) {
        Navigator.pop(context);
        // const MainScreen().navigateReplacement;
        ref.invalidate(getQuotationDetailsFutureProvider(quotationData.ipd));
        ref.invalidate(getQuotationListFutureProvider);

        QuotationDetailsScreen(quotationId: quotationData.ipd)
            .navigateReplacement;
        context.showBarMessage(context.tr.pricingSavedSuccessfully);
      }
    } catch (e) {
      if (context.mounted) {
        context.showBarMessage(
          context.tr.failedToSavePricing,
          isError: true,
        );
      }
    } finally {
      isLoading.value = false;
    }
  }
}

// Extension methods for copyWith functionality
extension QuotationPricingDetailExtension on QuotationPricingDetail {
  QuotationPricingDetail copyWith({
    String? productId,
    String? extraUserCost,
    List<QuotationPricingSubProduct>? subproducts,
  }) {
    return QuotationPricingDetail(
      productId: productId ?? this.productId,
      extraUserCost: extraUserCost ?? this.extraUserCost,
      subproducts: subproducts ?? this.subproducts,
    );
  }
}

extension QuotationPricingSubProductExtension on QuotationPricingSubProduct {
  QuotationPricingSubProduct copyWith({
    String? subProductId,
    String? cost,
  }) {
    return QuotationPricingSubProduct(
      subProductId: subProductId ?? this.subProductId,
      cost: cost ?? this.cost,
    );
  }
}

extension QuotationPaymentExtension on QuotationPayment {
  QuotationPayment copyWith({
    String? initialAmount,
    String? discount,
    String? payInstallments,
    String? payStartDate,
    String? payEvery,
  }) {
    return QuotationPayment(
      initialAmount: initialAmount ?? this.initialAmount,
      discount: discount ?? this.discount,
      payInstallments: payInstallments ?? this.payInstallments,
      payStartDate: payStartDate ?? this.payStartDate,
      payEvery: payEvery ?? this.payEvery,
    );
  }
}
