import 'package:flutter/material.dart';
import 'package:opti_tickets/src/core/shared/extensions/context_extensions.dart';
import 'package:opti_tickets/src/core/theme/color_manager.dart';
import 'package:opti_tickets/src/screens/quotations/models/quotation_model.dart';
import 'package:xr_helper/xr_helper.dart';

class QuotationActionButtons extends StatelessWidget {
  final QuotationDetailPermissions permissions;
  final QuotationDetailData quotationData;
  final Function(String action) onActionPressed;

  const QuotationActionButtons({
    super.key,
    required this.permissions,
    required this.quotationData,
    required this.onActionPressed,
  });

  @override
  Widget build(BuildContext context) {
    List<_ActionButtonItem> buttons = _getActionButtons(context);

    if (buttons.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      padding: const EdgeInsets.all(AppSpaces.padding16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            context.tr.actions,
            style: AppTextStyles.title.copyWith(
              fontWeight: FontWeight.w600,
              fontSize: 18,
            ),
          ),
          AppGaps.gap16,
          GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: buttons.length > 2 ? 3 : 2,
              crossAxisSpacing: 12,
              mainAxisSpacing: 12,
              childAspectRatio: buttons.length > 2 ? 1.1 : 1.5,
            ),
            itemCount: buttons.length,
            itemBuilder: (context, index) {
              return _buildActionCard(context, buttons[index]);
            },
          ),
        ],
      ),
    );
  }

  Widget _buildActionCard(BuildContext context, _ActionButtonItem item) {
    return InkWell(
      onTap: () => onActionPressed(item.action),
      borderRadius: BorderRadius.circular(AppRadius.radius12),
      child: Container(
        decoration: BoxDecoration(
          color: ColorManager.lightGrey.withOpacity(0.1),
          borderRadius: BorderRadius.circular(AppRadius.radius12),
          // border: Border.all(
          //   color: item.color.withOpacity(0.3),
          //   width: 1,
          // ),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: item.color.withOpacity(0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(
                item.icon,
                size: 24,
                color: item.color,
              ),
            ),
            AppGaps.gap8,
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 4),
              child: Text(
                item.title,
                textAlign: TextAlign.center,
                style: AppTextStyles.labelSmall.copyWith(
                  fontWeight: FontWeight.w600,
                  fontSize: 11,
                  color: ColorManager.darkGrey,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      ),
    );
  }

  List<_ActionButtonItem> _getActionButtons(BuildContext context) {
    List<_ActionButtonItem> buttons = [];

    // Status-based actions
    if (quotationData.status.canSave) {
      buttons.add(_ActionButtonItem(
        icon: Icons.edit,
        title: context.tr.edit,
        action: 'edit',
        color: Colors.green,
      ));
      buttons.add(_ActionButtonItem(
        icon: Icons.attach_money,
        title: context.tr.pricing,
        action: 'price',
        color: ColorManager.primaryColor,
      ));
    }

    if (quotationData.status.canApproveClient) {
      buttons.add(_ActionButtonItem(
        icon: Icons.edit,
        title: context.tr.edit,
        action: 'edit',
        color: Colors.orange,
      ));
      buttons.add(_ActionButtonItem(
        icon: Icons.check_circle,
        title: context.tr.clientApproval,
        action: 'client_approval',
        color: Colors.blue,
      ));
      buttons.add(_ActionButtonItem(
        icon: Icons.note,
        title: context.tr.clientNote,
        action: 'client_note',
        color: Colors.deepOrangeAccent,
      ));
    }

    if (quotationData.status.canIssueLicense) {
      buttons.add(_ActionButtonItem(
        icon: Icons.edit,
        title: context.tr.edit,
        action: 'edit',
        color: Colors.orange,
      ));
      buttons.add(_ActionButtonItem(
        icon: Icons.card_membership,
        title: context.tr.issueLicense,
        action: 'issue_license',
        color: Colors.green,
      ));
    }

    return buttons;
  }
}

class _ActionButtonItem {
  final IconData icon;
  final String title;
  final String action;
  final Color color;

  const _ActionButtonItem({
    required this.icon,
    required this.title,
    required this.action,
    required this.color,
  });
}
