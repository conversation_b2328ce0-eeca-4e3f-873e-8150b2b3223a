import 'package:flutter/material.dart';
import 'package:opti_tickets/src/core/shared/extensions/context_extensions.dart';
import 'package:opti_tickets/src/core/theme/color_manager.dart';
import 'package:opti_tickets/src/screens/quotations/models/quotation_model.dart';
import 'package:xr_helper/xr_helper.dart';

class QuotationCalculationFooter extends StatelessWidget {
  final QuotationDetailData quotationData;

  const QuotationCalculationFooter({
    super.key,
    required this.quotationData,
  });

  @override
  Widget build(BuildContext context) {
    if (!quotationData.status.canShowPrice) {
      return const SizedBox.shrink();
    }

    final calculations = _calculateTotals();

    return Container(
      margin: const EdgeInsets.only(
        right: AppSpaces.padding16,
        left: AppSpaces.padding16,
        bottom: AppSpaces.padding16,
      ),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppRadius.radius12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(AppSpaces.padding16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              context.tr.priceCalculation,
              style: AppTextStyles.title.copyWith(
                fontWeight: FontWeight.w600,
                fontSize: 18,
              ),
            ),
            AppGaps.gap16,
            _buildCalculationRow(
              context.tr.subtotal,
              _formatNumber(calculations.subtotal),
              isSubtotal: true,
            ),
            if (calculations.discount > 0) ...[
              AppGaps.gap8,
              _buildCalculationRow(
                context.tr.discount,
                _formatNumber(calculations.discount),
                isDiscount: true,
              ),
            ],
            if (calculations.vat > 0) ...[
              AppGaps.gap8,
              _buildCalculationRow(
                '${context.tr.vat} (${calculations.vatPercentage.toStringAsFixed(0)}%)',
                _formatNumber(calculations.vat),
                isVat: true,
              ),
            ],
            AppGaps.gap12,
            Container(
              height: 1,
              color: ColorManager.lightGrey.withOpacity(0.5),
            ),
            AppGaps.gap12,
            _buildCalculationRow(
              context.tr.total,
              _formatNumber(calculations.total),
              isTotal: true,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCalculationRow(
    String label,
    String value, {
    bool isSubtotal = false,
    bool isDiscount = false,
    bool isVat = false,
    bool isTotal = false,
  }) {
    Color valueColor = ColorManager.black;
    Color labelColor = ColorManager.black;
    FontWeight fontWeight = FontWeight.w500;

    if (isDiscount) {
      valueColor = Colors.red;
      labelColor = Colors.red;
    } else if (isVat) {
      valueColor = Colors.green;
    } else if (isTotal) {
      valueColor = ColorManager.primaryColor;
      fontWeight = FontWeight.w700;
    }

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: AppTextStyles.body.copyWith(
            fontWeight: isTotal ? FontWeight.w600 : FontWeight.w500,
            fontSize: isTotal ? 16 : 14,
            color: labelColor,
          ),
        ),
        Text(
          value,
          style: AppTextStyles.body.copyWith(
            fontWeight: fontWeight,
            fontSize: isTotal ? 16 : 14,
            color: valueColor,
          ),
        ),
      ],
    );
  }

  String _formatNumber(double number) {
    // Check if the number has meaningful decimal places
    if (number == number.roundToDouble()) {
      // No decimal places needed
      return number.toStringAsFixed(0);
    } else {
      // Has decimal places, format to 2 decimal places
      return number.toStringAsFixed(2);
    }
  }

  _QuotationCalculations _calculateTotals() {
    double subtotal = 0.0;
    double totalVatPercentage = 0.0;
    int serviceCount = 0;

    // Calculate subtotal from all subservices
    for (final service in quotationData.subs) {
      for (final subservice in service.subservices) {
        final cost = double.tryParse(subservice.subserviceCost) ?? 0.0;
        subtotal += cost;
      }

      // Accumulate VAT percentages
      if (service.vat > 0) {
        totalVatPercentage += service.vat;
        serviceCount++;
      }
    }

    // Calculate average VAT percentage if multiple services have VAT
    final avgVatPercentage =
        serviceCount > 0 ? totalVatPercentage / serviceCount : 0.0;

    // Calculate discount
    final discount = double.tryParse(quotationData.discount) ?? 0.0;

    // Calculate VAT on (subtotal - discount)
    final subtotalAfterDiscount = subtotal - discount;
    final vat = subtotalAfterDiscount * (avgVatPercentage / 100);

    // Calculate final total
    final total = subtotalAfterDiscount + vat;

    return _QuotationCalculations(
      subtotal: subtotal,
      discount: discount,
      vat: vat,
      vatPercentage: avgVatPercentage,
      total: total,
    );
  }
}

class _QuotationCalculations {
  final double subtotal;
  final double discount;
  final double vat;
  final double vatPercentage;
  final double total;

  const _QuotationCalculations({
    required this.subtotal,
    required this.discount,
    required this.vat,
    required this.vatPercentage,
    required this.total,
  });
}
